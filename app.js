// Enhanced Landing Page Builder Application
class LandingPageBuilder {
    constructor() {
        this.isEditMode = false; // Start in VIEW mode, not edit mode
        this.originalData = null;
        this.currentData = this.loadDataWithStyles();
        this.activeElement = null;
        this.hasTextSelection = false;
        
        // Undo/Redo system
        this.undoStack = [];
        this.redoStack = [];
        this.maxHistorySize = 50;
        this.isUndoRedoOperation = false;
        
        this.init();
    }

    // Initialize the application
    init() {
        this.renderContent();
        this.bindEvents();
        this.setupKeyboardShortcuts();
        this.setupSelectionListener();
        this.updateUndoRedoButtons();
        
        // FIXED: Ensure we start in VIEW mode
        this.ensureViewMode();
    }

    // FIXED: Ensure application starts in view mode
    ensureViewMode() {
        const body = document.body;
        const editToggle = document.getElementById('editToggle');
        const editToggleText = document.getElementById('editToggleText');
        const floatingToolbar = document.getElementById('floatingToolbar');
        const richTextToolbar = document.getElementById('richTextToolbar');

        // Make sure we're in view mode
        this.isEditMode = false;
        body.classList.remove('edit-mode', 'toolbar-visible');
        if (editToggle) {
            editToggle.classList.remove('active');
            editToggle.style.display = 'flex';
            editToggle.style.visibility = 'visible';
        }
        if (editToggleText) {
            editToggleText.textContent = 'Edit';
        }
        if (floatingToolbar) {
            floatingToolbar.classList.add('hidden');
        }
        if (richTextToolbar) {
            richTextToolbar.classList.add('hidden');
        }
        
        this.makeElementsNonEditable();
    }

    // Enhanced data structure with styles support
    getDefaultDataWithStyles() {
        return {
            hero: {
                heading: {
                    text: "Welcome to Our Amazing Service",
                    styles: {
                        fontFamily: "Arial, sans-serif",
                        fontSize: "48px",
                        fontWeight: "700",
                        color: "#ffffff",
                        textAlign: "center",
                        lineHeight: "1.2"
                    }
                },
                description: {
                    text: "Transform your business with our innovative solutions that drive growth and success",
                    styles: {
                        fontFamily: "Georgia, serif",
                        fontSize: "18px",
                        fontWeight: "400",
                        color: "#ffffff",
                        textAlign: "center",
                        lineHeight: "1.6"
                    }
                },
                buttonText: "Get Started",
                buttonUrl: "#services",
                backgroundImage: "https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
            },
            about: {
                heading: {
                    text: "About Us",
                    styles: {
                        fontFamily: "Arial, sans-serif",
                        fontSize: "36px",
                        fontWeight: "600",
                        color: "#2c3e50",
                        textAlign: "left"
                    }
                },
                description: {
                    text: "We are passionate about delivering excellence and innovation. Our team of experts works tirelessly to provide solutions that exceed expectations and drive meaningful results for our clients.",
                    styles: {
                        fontFamily: "Georgia, serif",
                        fontSize: "16px",
                        fontWeight: "400",
                        color: "#34495e",
                        lineHeight: "1.7"
                    }
                },
                image: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            },
            services: {
                heading: {
                    text: "Our Services",
                    styles: {
                        fontFamily: "Arial, sans-serif",
                        fontSize: "36px",
                        fontWeight: "600",
                        color: "#2c3e50",
                        textAlign: "center"
                    }
                },
                description: {
                    text: "Discover what we can do for you with our comprehensive range of professional services",
                    styles: {
                        fontFamily: "Georgia, serif",
                        fontSize: "16px",
                        fontWeight: "400",
                        color: "#34495e",
                        textAlign: "center",
                        lineHeight: "1.6"
                    }
                },
                items: [
                    {
                        title: {
                            text: "Web Development",
                            styles: {
                                fontFamily: "Arial, sans-serif",
                                fontSize: "20px",
                                fontWeight: "600",
                                color: "#2c3e50"
                            }
                        },
                        description: {
                            text: "Custom websites and web applications built with modern technologies",
                            styles: {
                                fontFamily: "Georgia, serif",
                                fontSize: "14px",
                                fontWeight: "400",
                                color: "#34495e",
                                lineHeight: "1.5"
                            }
                        },
                        image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    },
                    {
                        title: {
                            text: "Digital Marketing",
                            styles: {
                                fontFamily: "Arial, sans-serif",
                                fontSize: "20px",
                                fontWeight: "600",
                                color: "#2c3e50"
                            }
                        },
                        description: {
                            text: "Strategic marketing campaigns that boost your online presence",
                            styles: {
                                fontFamily: "Georgia, serif",
                                fontSize: "14px",
                                fontWeight: "400",
                                color: "#34495e",
                                lineHeight: "1.5"
                            }
                        },
                        image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    },
                    {
                        title: {
                            text: "Consulting",
                            styles: {
                                fontFamily: "Arial, sans-serif",
                                fontSize: "20px",
                                fontWeight: "600",
                                color: "#2c3e50"
                            }
                        },
                        description: {
                            text: "Expert guidance to help your business grow and succeed",
                            styles: {
                                fontFamily: "Georgia, serif",
                                fontSize: "14px",
                                fontWeight: "400",
                                color: "#34495e",
                                lineHeight: "1.5"
                            }
                        },
                        image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                    }
                ]
            },
            testimonials: {
                heading: {
                    text: "What Our Clients Say",
                    styles: {
                        fontFamily: "Arial, sans-serif",
                        fontSize: "36px",
                        fontWeight: "600",
                        color: "#2c3e50",
                        textAlign: "center"
                    }
                },
                items: [
                    {
                        name: {
                            text: "John Doe",
                            styles: {
                                fontFamily: "Arial, sans-serif",
                                fontSize: "18px",
                                fontWeight: "600",
                                color: "#2c3e50"
                            }
                        },
                        company: {
                            text: "ABC Corp",
                            styles: {
                                fontFamily: "Arial, sans-serif",
                                fontSize: "14px",
                                fontWeight: "400",
                                color: "#7f8c8d"
                            }
                        },
                        text: {
                            text: "Amazing service, highly recommended! The team delivered beyond our expectations.",
                            styles: {
                                fontFamily: "Georgia, serif",
                                fontSize: "16px",
                                fontWeight: "400",
                                color: "#34495e",
                                lineHeight: "1.6"
                            }
                        },
                        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
                    },
                    {
                        name: {
                            text: "Jane Smith",
                            styles: {
                                fontFamily: "Arial, sans-serif",
                                fontSize: "18px",
                                fontWeight: "600",
                                color: "#2c3e50"
                            }
                        },
                        company: {
                            text: "XYZ Inc",
                            styles: {
                                fontFamily: "Arial, sans-serif",
                                fontSize: "14px",
                                fontWeight: "400",
                                color: "#7f8c8d"
                            }
                        },
                        text: {
                            text: "Professional and reliable team. They transformed our digital presence completely.",
                            styles: {
                                fontFamily: "Georgia, serif",
                                fontSize: "16px",
                                fontWeight: "400",
                                color: "#34495e",
                                lineHeight: "1.6"
                            }
                        },
                        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
                    }
                ]
            },
            contact: {
                heading: {
                    text: "Get In Touch",
                    styles: {
                        fontFamily: "Arial, sans-serif",
                        fontSize: "36px",
                        fontWeight: "600",
                        color: "#2c3e50",
                        textAlign: "center"
                    }
                },
                description: {
                    text: "Ready to start your project? Contact us today and let's discuss how we can help you achieve your goals!",
                    styles: {
                        fontFamily: "Georgia, serif",
                        fontSize: "16px",
                        fontWeight: "400",
                        color: "#34495e",
                        textAlign: "center",
                        lineHeight: "1.6"
                    }
                },
                buttonText: "Contact Us",
                buttonUrl: "mailto:<EMAIL>"
            }
        };
    }

    // Load data with styles support
    loadDataWithStyles() {
        try {
            const saved = localStorage.getItem('landingPageData');
            if (saved) {
                const parsedData = JSON.parse(saved);
                console.log('Loaded data from localStorage:', parsedData);
                return this.migrateDataFormat(parsedData);
            }
            return this.getDefaultDataWithStyles();
        } catch (error) {
            console.error('Error loading data:', error);
            return this.getDefaultDataWithStyles();
        }
    }

    // Migrate old data format to support styles
    migrateDataFormat(data) {
        const defaultData = this.getDefaultDataWithStyles();
        
        const convertToStyledText = (value, defaultStyleData) => {
            if (typeof value === 'string') {
                return {
                    text: value,
                    styles: defaultStyleData?.styles || {}
                };
            }
            return value;
        };

        Object.keys(defaultData).forEach(section => {
            if (data[section]) {
                Object.keys(defaultData[section]).forEach(key => {
                    if (key === 'items' && Array.isArray(data[section][key])) {
                        data[section][key] = data[section][key].map((item, index) => {
                            const migrated = {};
                            Object.keys(item).forEach(itemKey => {
                                if (defaultData[section][key][index]?.[itemKey]) {
                                    migrated[itemKey] = convertToStyledText(
                                        item[itemKey],
                                        defaultData[section][key][index][itemKey]
                                    );
                                } else {
                                    migrated[itemKey] = item[itemKey];
                                }
                            });
                            return migrated;
                        });
                    } else if (defaultData[section][key] && typeof defaultData[section][key] === 'object' && defaultData[section][key].text !== undefined) {
                        data[section][key] = convertToStyledText(data[section][key], defaultData[section][key]);
                    }
                });
            }
        });

        return data;
    }

    // FIXED: Manual save data - ONLY way to save changes
    saveData() {
        try {
            const dataToSave = JSON.stringify(this.currentData, null, 2);
            localStorage.setItem('landingPageData', dataToSave);
            console.log('Data saved to localStorage:', this.currentData);
            this.showSaveSuccess();
            
            // Update original data reference
            this.originalData = JSON.parse(JSON.stringify(this.currentData));
            return true;
        } catch (error) {
            console.error('Error saving data:', error);
            this.showSaveError();
            return false;
        }
    }

    // FIXED: Show save success feedback
    showSaveSuccess() {
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            const originalText = saveBtn.textContent;
            const originalBackground = saveBtn.style.background;
            
            saveBtn.textContent = 'Saved!';
            saveBtn.style.background = 'var(--color-success)';
            
            setTimeout(() => {
                saveBtn.textContent = originalText;
                saveBtn.style.background = originalBackground;
            }, 2000);
        }
        this.showStatusMessage('Changes saved successfully!', 'success');
    }

    // Show save error feedback
    showSaveError() {
        this.showStatusMessage('Error saving changes - storage may be full', 'error');
    }

    // Add command to history for undo/redo
    addToHistory(command) {
        if (this.isUndoRedoOperation) return;
        
        this.undoStack.push(command);
        this.redoStack = [];
        
        if (this.undoStack.length > this.maxHistorySize) {
            this.undoStack.shift();
        }
        
        this.updateUndoRedoButtons();
    }

    // Undo operation
    undo() {
        if (this.undoStack.length === 0) return;
        
        const command = this.undoStack.pop();
        this.redoStack.push({
            type: command.type,
            field: command.field,
            newValue: command.newValue,
            oldValue: command.oldValue
        });
        
        this.isUndoRedoOperation = true;
        this.executeCommand(command.type, command.field, command.oldValue);
        this.isUndoRedoOperation = false;
        
        this.updateUndoRedoButtons();
        this.showStatusMessage('Undone', 'info');
    }

    // Redo operation
    redo() {
        if (this.redoStack.length === 0) return;
        
        const command = this.redoStack.pop();
        this.undoStack.push({
            type: command.type,
            field: command.field,
            newValue: command.oldValue,
            oldValue: command.newValue
        });
        
        this.isUndoRedoOperation = true;
        this.executeCommand(command.type, command.field, command.newValue);
        this.isUndoRedoOperation = false;
        
        this.updateUndoRedoButtons();
        this.showStatusMessage('Redone', 'info');
    }

    // Execute command for undo/redo
    executeCommand(type, field, value) {
        switch (type) {
            case 'textEdit':
                this.updateDataField(field, value);
                this.renderContent();
                break;
            case 'styleEdit':
                this.updateDataField(field, value);
                this.renderContent();
                break;
            case 'imageEdit':
                this.updateDataField(field, value);
                this.updateImages();
                break;
        }
    }

    // Update undo/redo button states
    updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undoBtn');
        const redoBtn = document.getElementById('redoBtn');
        
        if (undoBtn) {
            undoBtn.disabled = this.undoStack.length === 0;
        }
        if (redoBtn) {
            redoBtn.disabled = this.redoStack.length === 0;
        }
    }

    // FIXED: Setup text selection listener - only show toolbar when BOTH edit mode AND text selection
    setupSelectionListener() {
        document.addEventListener('selectionchange', () => {
            const selection = window.getSelection();
            const hasSelection = selection.rangeCount > 0 && !selection.isCollapsed;
            
            this.hasTextSelection = hasSelection;
            
            // Show rich text toolbar ONLY if in edit mode AND has text selection
            if (this.isEditMode && this.hasTextSelection) {
                const range = selection.getRangeAt(0);
                const container = range.commonAncestorContainer;
                const element = container.nodeType === Node.TEXT_NODE ? container.parentElement : container;
                
                if (element.classList.contains('editable-text') || element.closest('.editable-text')) {
                    this.activeElement = element.closest('.editable-text');
                    this.showRichTextToolbar();
                    this.updateToolbarState();
                } else {
                    this.hideRichTextToolbar();
                }
            } else {
                // Hide rich text toolbar when not in edit mode or no text selected
                this.hideRichTextToolbar();
            }
        });
    }

    // Bind all event listeners
    bindEvents() {
        const editToggle = document.getElementById('editToggle');
        if (editToggle) {
            editToggle.addEventListener('click', () => this.toggleEditMode());
        }

        const saveBtn = document.getElementById('saveBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const resetBtn = document.getElementById('resetBtn');
        const undoBtn = document.getElementById('undoBtn');
        const redoBtn = document.getElementById('redoBtn');

        // MANUAL SAVE ONLY - no auto-save
        if (saveBtn) saveBtn.addEventListener('click', () => this.saveChanges());
        if (cancelBtn) cancelBtn.addEventListener('click', () => this.cancelChanges());
        if (resetBtn) resetBtn.addEventListener('click', () => this.resetToDefault());
        if (undoBtn) undoBtn.addEventListener('click', () => this.undo());
        if (redoBtn) redoBtn.addEventListener('click', () => this.redo());

        this.setupRichTextToolbar();
        document.addEventListener('click', (e) => this.handleDocumentClick(e));
    }

    // Setup keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        if (this.isEditMode) this.saveChanges();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.toggleEditMode();
                        break;
                    case 'z':
                        e.preventDefault();
                        if (e.shiftKey) {
                            this.redo();
                        } else {
                            this.undo();
                        }
                        break;
                    case 'y':
                        e.preventDefault();
                        this.redo();
                        break;
                }
            }
            if (e.key === 'Escape' && this.isEditMode) {
                this.cancelChanges();
            }
        });
    }

    // FIXED: Toggle edit mode with proper toolbar management
    toggleEditMode() {
        this.isEditMode = !this.isEditMode;
        const body = document.body;
        const editToggle = document.getElementById('editToggle');
        const editToggleText = document.getElementById('editToggleText');
        const floatingToolbar = document.getElementById('floatingToolbar');

        if (this.isEditMode) {
            this.originalData = JSON.parse(JSON.stringify(this.currentData));
            
            body.classList.add('edit-mode');
            editToggle.classList.add('active');
            editToggleText.textContent = 'Exit Edit';
            floatingToolbar.classList.remove('hidden');
            
            this.makeElementsEditable();
            this.updateUndoRedoButtons();
            
            this.showStatusMessage('Edit mode enabled. Select text to format it, click images to change them.', 'info');
        } else {
            body.classList.remove('edit-mode', 'toolbar-visible');
            editToggle.classList.remove('active');
            editToggleText.textContent = 'Edit';
            floatingToolbar.classList.add('hidden');
            
            // Always hide rich text toolbar when exiting edit mode
            this.hideRichTextToolbar();
            this.hasTextSelection = false;
            
            this.makeElementsNonEditable();
        }
        
        this.updateToolbarVisibility();
    }

    // Update toolbar visibility based on edit mode and text selection
    updateToolbarVisibility() {
        const richTextToolbar = document.getElementById('richTextToolbar');
        const floatingToolbar = document.getElementById('floatingToolbar');
        
        // Main toolbar visibility based on edit mode
        if (this.isEditMode) {
            floatingToolbar.classList.remove('hidden');
        } else {
            floatingToolbar.classList.add('hidden');
        }
        
        // Rich text toolbar visibility based on edit mode AND text selection
        if (this.isEditMode && this.hasTextSelection) {
            richTextToolbar.classList.remove('hidden');
            document.body.classList.add('toolbar-visible');
        } else {
            richTextToolbar.classList.add('hidden');
            document.body.classList.remove('toolbar-visible');
        }
    }

    // Make elements editable
    makeElementsEditable() {
        document.querySelectorAll('.editable-text').forEach(element => {
            element.contentEditable = true;
            element.style.cursor = 'text';
            
            element.removeEventListener('input', this.handleTextEdit);
            element.removeEventListener('focus', this.handleTextFocus);
            element.removeEventListener('blur', this.handleTextBlur);
            
            element.addEventListener('input', (e) => this.handleTextEdit(e));
            element.addEventListener('focus', (e) => this.handleTextFocus(e));
            element.addEventListener('blur', (e) => this.handleTextBlur(e));
        });

        document.querySelectorAll('.editable-button').forEach(element => {
            element.addEventListener('click', (e) => this.handleButtonEdit(e));
        });

        document.querySelectorAll('.image-container').forEach(container => {
            this.setupImageEditing(container);
        });
    }

    // Make elements non-editable
    makeElementsNonEditable() {
        document.querySelectorAll('.editable-text').forEach(element => {
            element.contentEditable = false;
            element.style.cursor = 'default';
            element.classList.remove('selected');
        });

        document.querySelectorAll('.editable-button').forEach(element => {
            element.replaceWith(element.cloneNode(true));
        });

        this.updateButtons();
    }

    // Handle text editing - NO AUTO-SAVE
    handleTextEdit(e) {
        const element = e.target;
        const field = element.dataset.field;
        if (!field) return;

        const oldValue = this.getDataField(field + '.text') || this.getDataField(field);
        const newValue = element.textContent || element.innerText;

        if (oldValue !== newValue) {
            this.addToHistory({
                type: 'textEdit',
                field: field + '.text',
                oldValue: oldValue,
                newValue: newValue
            });

            this.updateDataField(field + '.text', newValue);
            // NO AUTO-SAVE - removed this.autoSave();
        }
    }

    // Handle text focus
    handleTextFocus(e) {
        this.activeElement = e.target;
        e.target.classList.add('selected');
    }

    // Handle text blur
    handleTextBlur(e) {
        e.target.classList.remove('selected');
        setTimeout(() => {
            const activeEl = document.activeElement;
            if (!activeEl || !activeEl.closest('#richTextToolbar')) {
                this.hideRichTextToolbar();
            }
        }, 100);
    }

    // Handle button editing
    handleButtonEdit(e) {
        e.preventDefault();
        const button = e.target;
        const textField = button.dataset.textField;
        const urlField = button.dataset.urlField;

        this.showButtonEditModal(button, textField, urlField);
    }

    // Show button edit modal
    showButtonEditModal(button, textField, urlField) {
        const existingModal = document.querySelector('.modal-overlay');
        if (existingModal) existingModal.remove();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        modal.innerHTML = `
            <div class="modal-content">
                <h3>Edit Button</h3>
                <div class="form-group">
                    <label class="form-label">Button Text</label>
                    <input type="text" class="form-control" id="buttonText" value="${this.getDataField(textField)}">
                </div>
                <div class="form-group">
                    <label class="form-label">Button URL</label>
                    <input type="text" class="form-control" id="buttonUrl" value="${this.getDataField(urlField)}">
                </div>
                <div class="modal-actions">
                    <button class="btn btn--primary" id="saveButton">Save</button>
                    <button class="btn btn--secondary" id="cancelButton">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        modal.querySelector('#saveButton').addEventListener('click', () => {
            const newText = modal.querySelector('#buttonText').value;
            const newUrl = modal.querySelector('#buttonUrl').value;
            
            const oldText = this.getDataField(textField);
            const oldUrl = this.getDataField(urlField);
            
            if (oldText !== newText || oldUrl !== newUrl) {
                this.addToHistory({
                    type: 'textEdit',
                    field: textField,
                    oldValue: oldText,
                    newValue: newText
                });
            }
            
            this.updateDataField(textField, newText);
            this.updateDataField(urlField, newUrl);
            
            button.textContent = newText;
            button.href = newUrl;
            
            // NO AUTO-SAVE - removed this.autoSave();
            document.body.removeChild(modal);
        });

        modal.querySelector('#cancelButton').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    // Setup image editing with single-click functionality
    setupImageEditing(container) {
        const editBtn = container.querySelector('.image-edit-btn');
        const input = container.querySelector('.image-upload-input');

        if (editBtn && input) {
            editBtn.style.display = 'flex';
            
            editBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                input.click();
            });
            
            input.addEventListener('change', (e) => {
                if (e.target.files && e.target.files[0]) {
                    this.handleImageUpload(e, container);
                }
            });
        }

        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            container.classList.add('drag-over');
        });

        container.addEventListener('dragleave', (e) => {
            e.preventDefault();
            if (!container.contains(e.relatedTarget)) {
                container.classList.remove('drag-over');
            }
        });

        container.addEventListener('drop', (e) => {
            e.preventDefault();
            container.classList.remove('drag-over');
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                this.processImageFile(files[0], container);
            }
        });
    }

    // Handle image upload
    handleImageUpload(e, container) {
        const file = e.target.files[0];
        if (!file) return;
        
        if (!file.type.startsWith('image/')) {
            this.showStatusMessage('Please select a valid image file', 'error');
            return;
        }
        
        if (file.size > 5 * 1024 * 1024) {
            this.showStatusMessage('Image size must be less than 5MB', 'error');
            return;
        }
        
        this.processImageFile(file, container);
        e.target.value = '';
    }

    // Process uploaded image file - NO AUTO-SAVE
    processImageFile(file, container) {
        const field = container.dataset.field;
        if (!field) return;
        
        container.classList.add('loading');
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = container.querySelector('img');
            const oldValue = this.getDataField(field);
            const newValue = e.target.result;
            
            if (img) {
                this.addToHistory({
                    type: 'imageEdit',
                    field: field,
                    oldValue: oldValue,
                    newValue: newValue
                });
                
                img.src = newValue;
                this.updateDataField(field, newValue);
                // NO AUTO-SAVE - removed this.autoSave();
                this.showStatusMessage('Image updated successfully! Click Save to persist changes.', 'success');
            }
            
            container.classList.remove('loading');
        };
        
        reader.onerror = () => {
            container.classList.remove('loading');
            this.showStatusMessage('Error reading image file', 'error');
        };
        
        reader.readAsDataURL(file);
    }

    // Setup comprehensive rich text toolbar
    setupRichTextToolbar() {
        const fontFamily = document.getElementById('fontFamily');
        if (fontFamily) {
            fontFamily.addEventListener('change', (e) => {
                this.applyFormatting('fontFamily', e.target.value);
            });
        }

        const fontSize = document.getElementById('fontSize');
        if (fontSize) {
            fontSize.addEventListener('change', (e) => {
                this.applyFormatting('fontSize', e.target.value);
            });
        }

        const fontWeight = document.getElementById('fontWeight');
        if (fontWeight) {
            fontWeight.addEventListener('change', (e) => {
                this.applyFormatting('fontWeight', e.target.value);
            });
        }

        const fontColor = document.getElementById('fontColor');
        if (fontColor) {
            fontColor.addEventListener('change', (e) => {
                this.applyFormatting('color', e.target.value);
            });
        }

        const backgroundColor = document.getElementById('backgroundColor');
        if (backgroundColor) {
            backgroundColor.addEventListener('change', (e) => {
                this.applyFormatting('backgroundColor', e.target.value);
            });
        }

        ['alignLeft', 'alignCenter', 'alignRight', 'alignJustify'].forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const alignment = id.replace('align', '').toLowerCase();
                    this.applyFormatting('textAlign', alignment);
                });
            }
        });

        ['textTransform', 'textDecoration', 'fontStyle'].forEach(id => {
            const select = document.getElementById(id);
            if (select) {
                select.addEventListener('change', (e) => {
                    this.applyFormatting(id, e.target.value);
                });
            }
        });

        ['lineHeight', 'letterSpacing', 'wordSpacing'].forEach(id => {
            const select = document.getElementById(id);
            if (select) {
                select.addEventListener('change', (e) => {
                    this.applyFormatting(id, e.target.value);
                });
            }
        });

        const boldBtn = document.getElementById('boldBtn');
        const italicBtn = document.getElementById('italicBtn');
        const underlineBtn = document.getElementById('underlineBtn');

        if (boldBtn) {
            boldBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.applyFormatting('fontWeight', boldBtn.classList.contains('active') ? '400' : '700');
            });
        }

        if (italicBtn) {
            italicBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.applyFormatting('fontStyle', italicBtn.classList.contains('active') ? 'normal' : 'italic');
            });
        }

        if (underlineBtn) {
            underlineBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.applyFormatting('textDecoration', underlineBtn.classList.contains('active') ? 'none' : 'underline');
            });
        }

        const createLink = document.getElementById('createLink');
        if (createLink) {
            createLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.createLink();
            });
        }

        const clearFormatting = document.getElementById('clearFormatting');
        if (clearFormatting) {
            clearFormatting.addEventListener('click', (e) => {
                e.preventDefault();
                this.clearFormatting();
            });
        }
    }

    // Apply formatting - NO AUTO-SAVE
    applyFormatting(property, value) {
        if (!this.activeElement || !this.isEditMode) return;

        const selection = window.getSelection();
        
        if (selection.rangeCount > 0 && !selection.isCollapsed) {
            this.applyFormattingToSelection(property, value);
        } else {
            this.applyFormattingToElement(property, value);
        }
    }

    // Apply formatting to selected text range - NO AUTO-SAVE
    applyFormattingToSelection(property, value) {
        const selection = window.getSelection();
        if (selection.rangeCount === 0) return;

        const range = selection.getRangeAt(0);
        const span = document.createElement('span');
        span.style[property] = value;

        try {
            range.surroundContents(span);
            this.updateElementData();
        } catch (e) {
            const contents = range.extractContents();
            span.appendChild(contents);
            range.insertNode(span);
            this.updateElementData();
        }

        selection.removeAllRanges();
        // NO AUTO-SAVE - removed this.autoSave();
    }

    // Apply formatting to entire element - NO AUTO-SAVE
    applyFormattingToElement(property, value) {
        if (!this.activeElement) return;

        const field = this.activeElement.dataset.field;
        if (!field) return;

        const oldStyles = this.getDataField(field + '.styles') || {};
        const newStyles = { ...oldStyles, [property]: value };

        this.addToHistory({
            type: 'styleEdit',
            field: field + '.styles',
            oldValue: oldStyles,
            newValue: newStyles
        });

        this.updateDataField(field + '.styles', newStyles);
        this.applyStylesToElement(this.activeElement, newStyles);
        // NO AUTO-SAVE - removed this.autoSave();
    }

    // Update element data after formatting changes
    updateElementData() {
        if (!this.activeElement) return;

        const field = this.activeElement.dataset.field;
        if (!field) return;

        const content = this.activeElement.innerHTML;
        const textContent = this.activeElement.textContent;

        this.updateDataField(field + '.text', textContent);
        this.updateDataField(field + '.html', content);
    }

    // Create link
    createLink() {
        const selection = window.getSelection();
        if (selection.rangeCount === 0) return;

        const url = prompt('Enter URL:');
        if (!url) return;

        const range = selection.getRangeAt(0);
        const link = document.createElement('a');
        link.href = url;
        link.target = '_blank';

        try {
            range.surroundContents(link);
            this.updateElementData();
            // NO AUTO-SAVE - removed this.autoSave();
        } catch (e) {
            console.error('Error creating link:', e);
        }
    }

    // Clear formatting - NO AUTO-SAVE
    clearFormatting() {
        if (!this.activeElement) return;

        const selection = window.getSelection();
        
        if (selection.rangeCount > 0 && !selection.isCollapsed) {
            const range = selection.getRangeAt(0);
            const contents = range.extractContents();
            const textNode = document.createTextNode(contents.textContent);
            range.insertNode(textNode);
            this.updateElementData();
        } else {
            const field = this.activeElement.dataset.field;
            if (field) {
                const defaultStyles = {};
                this.updateDataField(field + '.styles', defaultStyles);
                this.applyStylesToElement(this.activeElement, defaultStyles);
            }
        }

        // NO AUTO-SAVE - removed this.autoSave();
    }

    // Show rich text toolbar (fixed at top)
    showRichTextToolbar() {
        const toolbar = document.getElementById('richTextToolbar');
        if (!toolbar || !this.hasTextSelection || !this.isEditMode) return;
        
        toolbar.classList.remove('hidden');
        document.body.classList.add('toolbar-visible');
        
        this.updateToolbarState();
    }

    // Hide rich text toolbar
    hideRichTextToolbar() {
        const toolbar = document.getElementById('richTextToolbar');
        if (toolbar) {
            toolbar.classList.add('hidden');
            document.body.classList.remove('toolbar-visible');
        }
    }

    // Update toolbar states
    updateToolbarState() {
        if (!this.activeElement) return;

        const field = this.activeElement.dataset.field;
        if (!field) return;

        const styles = this.getDataField(field + '.styles') || {};

        this.updateSelect('fontFamily', styles.fontFamily);
        this.updateSelect('fontSize', styles.fontSize);
        this.updateSelect('fontWeight', styles.fontWeight);
        this.updateSelect('textTransform', styles.textTransform);
        this.updateSelect('textDecoration', styles.textDecoration);
        this.updateSelect('fontStyle', styles.fontStyle);
        this.updateSelect('lineHeight', styles.lineHeight);
        this.updateSelect('letterSpacing', styles.letterSpacing);
        this.updateSelect('wordSpacing', styles.wordSpacing);

        this.updateColorInput('fontColor', styles.color);
        this.updateColorInput('backgroundColor', styles.backgroundColor);

        this.updateAlignmentButtons(styles.textAlign);
        this.updateFormatButtons(styles);
    }

    // Helper methods for updating toolbar controls
    updateSelect(id, value) {
        const select = document.getElementById(id);
        if (select && value) {
            select.value = value;
        }
    }

    updateColorInput(id, value) {
        const input = document.getElementById(id);
        if (input && value) {
            input.value = value;
        }
    }

    updateAlignmentButtons(alignment) {
        ['alignLeft', 'alignCenter', 'alignRight', 'alignJustify'].forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                const btnAlignment = id.replace('align', '').toLowerCase();
                btn.classList.toggle('active', alignment === btnAlignment);
            }
        });
    }

    updateFormatButtons(styles) {
        const boldBtn = document.getElementById('boldBtn');
        const italicBtn = document.getElementById('italicBtn');
        const underlineBtn = document.getElementById('underlineBtn');

        if (boldBtn) {
            boldBtn.classList.toggle('active', styles.fontWeight === '700' || styles.fontWeight === 'bold');
        }
        if (italicBtn) {
            italicBtn.classList.toggle('active', styles.fontStyle === 'italic');
        }
        if (underlineBtn) {
            underlineBtn.classList.toggle('active', styles.textDecoration === 'underline');
        }
    }

    // Handle document clicks
    handleDocumentClick(e) {
        const richTextToolbar = document.getElementById('richTextToolbar');
        if (richTextToolbar && 
            !e.target.closest('#richTextToolbar') && 
            !e.target.classList.contains('editable-text') &&
            !e.target.closest('.editable-text')) {
            this.hasTextSelection = false;
            this.hideRichTextToolbar();
        }
    }

    // Apply styles to element
    applyStylesToElement(element, styles) {
        Object.keys(styles).forEach(property => {
            if (styles[property]) {
                element.style[property] = styles[property];
            }
        });
    }

    // Update data field with dot notation support
    updateDataField(field, value) {
        const keys = field.split('.');
        let obj = this.currentData;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            const nextKey = keys[i + 1];
            
            if (!obj[key]) {
                obj[key] = isNaN(nextKey) ? {} : [];
            }
            obj = obj[key];
        }
        
        const lastKey = keys[keys.length - 1];
        obj[lastKey] = value;
    }

    // Get data field value with dot notation support
    getDataField(field) {
        const keys = field.split('.');
        let obj = this.currentData;
        
        for (const key of keys) {
            if (obj && obj[key] !== undefined) {
                obj = obj[key];
            } else {
                return null;
            }
        }
        
        return obj;
    }

    // Render all content with styles
    renderContent() {
        this.updateTextContent();
        this.updateImages();
        this.updateButtons();
        this.renderServices();
        this.renderTestimonials();
    }

    // Update text content with styles
    updateTextContent() {
        document.querySelectorAll('[data-field]').forEach(element => {
            const field = element.dataset.field;
            if (!field) return;
            
            const data = this.getDataField(field);
            
            if (element.classList.contains('editable-text')) {
                if (typeof data === 'object' && data.text !== undefined) {
                    element.textContent = data.text;
                    if (data.styles) {
                        this.applyStylesToElement(element, data.styles);
                    }
                } else if (typeof data === 'string') {
                    element.textContent = data;
                }
            }
        });
    }

    // Update images
    updateImages() {
        document.querySelectorAll('.image-container[data-field]').forEach(container => {
            const field = container.dataset.field;
            const img = container.querySelector('img');
            const value = this.getDataField(field);
            if (img && value) {
                img.src = value;
            }
        });
    }

    // Update buttons
    updateButtons() {
        document.querySelectorAll('.editable-button').forEach(button => {
            const textField = button.dataset.textField;
            const urlField = button.dataset.urlField;
            
            if (textField) {
                const text = this.getDataField(textField);
                if (text) button.textContent = text;
            }
            if (urlField) {
                const url = this.getDataField(urlField);
                if (url) button.href = url;
            }
        });
    }

    // Render services with styles
    renderServices() {
        const grid = document.getElementById('servicesGrid');
        if (!grid) return;
        
        grid.innerHTML = '';
        
        if (!this.currentData.services?.items) return;
        
        this.currentData.services.items.forEach((service, index) => {
            const item = document.createElement('div');
            item.className = 'service-item';
            
            const titleData = service.title || {};
            const descData = service.description || {};
            
            item.innerHTML = `
                <div class="image-container" data-field="services.items.${index}.image">
                    <img src="${service.image}" alt="${typeof titleData === 'object' ? titleData.text : titleData}">
                    <button class="image-edit-btn hidden">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                            <circle cx="12" cy="13" r="4"></circle>
                        </svg>
                    </button>
                    <input type="file" class="image-upload-input hidden" accept="image/*">
                </div>
                <h3 class="editable-text" data-field="services.items.${index}.title">${typeof titleData === 'object' ? titleData.text : titleData}</h3>
                <p class="editable-text" data-field="services.items.${index}.description">${typeof descData === 'object' ? descData.text : descData}</p>
            `;
            
            grid.appendChild(item);
            
            if (typeof titleData === 'object' && titleData.styles) {
                const titleEl = item.querySelector('h3');
                this.applyStylesToElement(titleEl, titleData.styles);
            }
            
            if (typeof descData === 'object' && descData.styles) {
                const descEl = item.querySelector('p');
                this.applyStylesToElement(descEl, descData.styles);
            }
        });
        
        if (this.isEditMode) {
            grid.querySelectorAll('.image-container').forEach(container => {
                this.setupImageEditing(container);
            });
        }
    }

    // Render testimonials with styles
    renderTestimonials() {
        const grid = document.getElementById('testimonialsGrid');
        if (!grid) return;
        
        grid.innerHTML = '';
        
        if (!this.currentData.testimonials?.items) return;
        
        this.currentData.testimonials.items.forEach((testimonial, index) => {
            const item = document.createElement('div');
            item.className = 'testimonial-item';
            
            const nameData = testimonial.name || {};
            const companyData = testimonial.company || {};
            const textData = testimonial.text || {};
            
            item.innerHTML = `
                <div class="testimonial-header">
                    <div class="testimonial-avatar">
                        <div class="image-container" data-field="testimonials.items.${index}.avatar">
                            <img src="${testimonial.avatar}" alt="${typeof nameData === 'object' ? nameData.text : nameData}">
                            <button class="image-edit-btn hidden">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                                    <circle cx="12" cy="13" r="4"></circle>
                                </svg>
                            </button>
                            <input type="file" class="image-upload-input hidden" accept="image/*">
                        </div>
                    </div>
                    <div class="testimonial-info">
                        <h4 class="editable-text" data-field="testimonials.items.${index}.name">${typeof nameData === 'object' ? nameData.text : nameData}</h4>
                        <p class="editable-text" data-field="testimonials.items.${index}.company">${typeof companyData === 'object' ? companyData.text : companyData}</p>
                    </div>
                </div>
                <p class="testimonial-text editable-text" data-field="testimonials.items.${index}.text">${typeof textData === 'object' ? textData.text : textData}</p>
            `;
            
            grid.appendChild(item);
            
            if (typeof nameData === 'object' && nameData.styles) {
                const nameEl = item.querySelector('h4');
                this.applyStylesToElement(nameEl, nameData.styles);
            }
            
            if (typeof companyData === 'object' && companyData.styles) {
                const companyEl = item.querySelector('.testimonial-info p');
                this.applyStylesToElement(companyEl, companyData.styles);
            }
            
            if (typeof textData === 'object' && textData.styles) {
                const textEl = item.querySelector('.testimonial-text');
                this.applyStylesToElement(textEl, textData.styles);
            }
        });
        
        if (this.isEditMode) {
            grid.querySelectorAll('.image-container').forEach(container => {
                this.setupImageEditing(container);
            });
        }
    }

    // MANUAL SAVE ONLY - Save changes with validation
    saveChanges() {
        try {
            if (this.saveData()) {
                this.originalData = JSON.parse(JSON.stringify(this.currentData));
                // Success feedback already shown in saveData()
            }
        } catch (error) {
            console.error('Save error:', error);
            this.showStatusMessage('Error saving changes', 'error');
        }
    }

    // Cancel changes
    cancelChanges() {
        if (this.originalData) {
            this.currentData = JSON.parse(JSON.stringify(this.originalData));
            this.renderContent();
            this.showStatusMessage('Changes cancelled', 'info');
            
            this.undoStack = [];
            this.redoStack = [];
            this.updateUndoRedoButtons();
        }
        this.toggleEditMode();
    }

    // Reset to default
    resetToDefault() {
        if (confirm('Are you sure you want to reset all content to default? This cannot be undone.')) {
            this.currentData = this.getDefaultDataWithStyles();
            this.renderContent();
            this.saveData();
            this.showStatusMessage('Content reset to default', 'success');
            
            this.undoStack = [];
            this.redoStack = [];
            this.updateUndoRedoButtons();
        }
    }

    // Enhanced status message
    showStatusMessage(message, type = 'info') {
        const existing = document.querySelector('.status-message');
        if (existing) existing.remove();

        const statusEl = document.createElement('div');
        statusEl.className = `status-message ${type}`;
        statusEl.textContent = message;

        document.body.appendChild(statusEl);

        setTimeout(() => {
            if (statusEl.parentNode) {
                statusEl.style.opacity = '0';
                statusEl.style.transform = 'translateX(-50%) translateY(-10px)';
                setTimeout(() => {
                    if (statusEl.parentNode) {
                        statusEl.remove();
                    }
                }, 300);
            }
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.landingPageBuilder = new LandingPageBuilder();
});