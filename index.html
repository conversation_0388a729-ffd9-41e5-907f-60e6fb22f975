<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Landing Page Builder</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- FIXED: Edit Mode Toggle Button (always visible in view mode) -->
    <button id="editToggle" class="edit-toggle-btn">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2-2v-7"></path>
            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
        </svg>
        <span id="editToggleText">Edit</span>
    </button>

    <!-- FIXED: Floating Toolbar (hidden by default, positioned to avoid overlap) -->
    <div id="floatingToolbar" class="floating-toolbar hidden">
        <button id="undoBtn" class="btn btn--outline btn--sm" title="Undo (Ctrl+Z)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 7v6h6"></path>
                <path d="m21 17-8-8 8-8"></path>
                <path d="m11 7 8 8-8 8"></path>
            </svg>
        </button>
        <button id="redoBtn" class="btn btn--outline btn--sm" title="Redo (Ctrl+Y)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 7v6h-6"></path>
                <path d="m3 17 8-8-8-8"></path>
                <path d="m13 7-8 8 8 8"></path>
            </svg>
        </button>
        <div class="toolbar-separator"></div>
        <button id="saveBtn" class="btn btn--primary btn--sm">Save</button>
        <button id="cancelBtn" class="btn btn--secondary btn--sm">Cancel</button>
        <button id="resetBtn" class="btn btn--outline btn--sm">Reset</button>
    </div>

    <!-- FIXED: Rich Text Editor Toolbar (hidden by default, fixed at very top to avoid overlap) -->
    <div id="richTextToolbar" class="rich-text-toolbar hidden">
        <div class="toolbar-section">
            <select id="fontFamily" class="toolbar-select" title="Font Family">
                <option value="Arial, sans-serif">Arial</option>
                <option value="Helvetica, sans-serif">Helvetica</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="'Times New Roman', serif">Times New Roman</option>
                <option value="Verdana, sans-serif">Verdana</option>
                <option value="'Courier New', monospace">Courier New</option>
                <option value="Impact, sans-serif">Impact</option>
                <option value="'Comic Sans MS', cursive">Comic Sans MS</option>
            </select>
            
            <select id="fontSize" class="toolbar-select" title="Font Size">
                <option value="8px">8px</option>
                <option value="10px">10px</option>
                <option value="12px">12px</option>
                <option value="14px">14px</option>
                <option value="16px">16px</option>
                <option value="18px">18px</option>
                <option value="20px">20px</option>
                <option value="24px">24px</option>
                <option value="28px">28px</option>
                <option value="32px">32px</option>
                <option value="36px">36px</option>
                <option value="48px">48px</option>
                <option value="72px">72px</option>
            </select>
            
            <select id="fontWeight" class="toolbar-select" title="Font Weight">
                <option value="100">100 (Thin)</option>
                <option value="200">200 (Extra Light)</option>
                <option value="300">300 (Light)</option>
                <option value="400">400 (Normal)</option>
                <option value="500">500 (Medium)</option>
                <option value="600">600 (Semi Bold)</option>
                <option value="700">700 (Bold)</option>
                <option value="800">800 (Extra Bold)</option>
                <option value="900">900 (Black)</option>
            </select>
        </div>

        <div class="toolbar-separator"></div>

        <div class="toolbar-section">
            <input type="color" id="fontColor" class="color-input" title="Text Color" value="#000000">
            <input type="color" id="backgroundColor" class="color-input" title="Background Color" value="#ffffff">
        </div>

        <div class="toolbar-separator"></div>

        <div class="toolbar-section">
            <button id="alignLeft" class="toolbar-btn" title="Align Left">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="21" y1="10" x2="3" y2="10"></line>
                    <line x1="15" y1="6" x2="3" y2="6"></line>
                    <line x1="17" y1="14" x2="3" y2="14"></line>
                    <line x1="13" y1="18" x2="3" y2="18"></line>
                </svg>
            </button>
            <button id="alignCenter" class="toolbar-btn" title="Align Center">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="10" x2="6" y2="10"></line>
                    <line x1="21" y1="6" x2="3" y2="6"></line>
                    <line x1="21" y1="14" x2="3" y2="14"></line>
                    <line x1="18" y1="18" x2="6" y2="18"></line>
                </svg>
            </button>
            <button id="alignRight" class="toolbar-btn" title="Align Right">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="21" y1="10" x2="3" y2="10"></line>
                    <line x1="21" y1="6" x2="9" y2="6"></line>
                    <line x1="21" y1="14" x2="7" y2="14"></line>
                    <line x1="21" y1="18" x2="11" y2="18"></line>
                </svg>
            </button>
            <button id="alignJustify" class="toolbar-btn" title="Justify">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="21" y1="10" x2="3" y2="10"></line>
                    <line x1="21" y1="6" x2="3" y2="6"></line>
                    <line x1="21" y1="14" x2="3" y2="14"></line>
                    <line x1="21" y1="18" x2="3" y2="18"></line>
                </svg>
            </button>
        </div>

        <div class="toolbar-separator"></div>

        <div class="toolbar-section">
            <button id="boldBtn" class="toolbar-btn" data-command="bold" title="Bold"><strong>B</strong></button>
            <button id="italicBtn" class="toolbar-btn" data-command="italic" title="Italic"><em>I</em></button>
            <button id="underlineBtn" class="toolbar-btn" data-command="underline" title="Underline"><u>U</u></button>
        </div>

        <div class="toolbar-separator"></div>

        <div class="toolbar-section">
            <select id="textTransform" class="toolbar-select" title="Text Transform">
                <option value="none">None</option>
                <option value="uppercase">UPPERCASE</option>
                <option value="lowercase">lowercase</option>
                <option value="capitalize">Capitalize</option>
            </select>
            
            <select id="textDecoration" class="toolbar-select" title="Text Decoration">
                <option value="none">None</option>
                <option value="underline">Underline</option>
                <option value="overline">Overline</option>
                <option value="line-through">Line-through</option>
            </select>
            
            <select id="fontStyle" class="toolbar-select" title="Font Style">
                <option value="normal">Normal</option>
                <option value="italic">Italic</option>
                <option value="oblique">Oblique</option>
            </select>
        </div>

        <div class="toolbar-separator"></div>

        <div class="toolbar-section">
            <select id="lineHeight" class="toolbar-select" title="Line Height">
                <option value="1.0">1.0</option>
                <option value="1.2">1.2</option>
                <option value="1.4">1.4</option>
                <option value="1.6">1.6</option>
                <option value="1.8">1.8</option>
                <option value="2.0">2.0</option>
            </select>
            
            <select id="letterSpacing" class="toolbar-select" title="Letter Spacing">
                <option value="normal">Normal</option>
                <option value="0.5px">0.5px</option>
                <option value="1px">1px</option>
                <option value="2px">2px</option>
                <option value="3px">3px</option>
            </select>
            
            <select id="wordSpacing" class="toolbar-select" title="Word Spacing">
                <option value="normal">Normal</option>
                <option value="2px">2px</option>
                <option value="4px">4px</option>
                <option value="6px">6px</option>
                <option value="8px">8px</option>
            </select>
        </div>

        <div class="toolbar-separator"></div>

        <div class="toolbar-section">
            <button id="createLink" class="toolbar-btn" title="Create Link">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.72"></path>
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.72-1.72"></path>
                </svg>
            </button>
            <button id="clearFormatting" class="toolbar-btn" title="Clear Formatting">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 6h18l-1 14H4L3 6z"></path>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                    <line x1="10" y1="11" x2="10" y2="17"></line>
                    <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <main id="mainContent">
        <!-- Hero Section -->
        <section id="hero" class="hero-section">
            <div class="hero-content">
                <h1 class="editable-text" data-field="hero.heading">Welcome to Our Amazing Service</h1>
                <p class="editable-text" data-field="hero.description">Transform your business with our innovative solutions that drive growth and success</p>
                <a href="#services" class="btn btn--primary btn--lg editable-button" data-text-field="hero.buttonText" data-url-field="hero.buttonUrl">Get Started</a>
            </div>
            <div class="image-container" data-field="hero.backgroundImage">
                <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="Hero Background">
                <button class="image-edit-btn hidden">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                        <circle cx="12" cy="13" r="4"></circle>
                    </svg>
                </button>
                <input type="file" class="image-upload-input hidden" accept="image/*">
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about-section">
            <div class="container">
                <div class="about-content">
                    <div class="text-content">
                        <h2 class="editable-text" data-field="about.heading">About Us</h2>
                        <p class="editable-text" data-field="about.description">We are passionate about delivering excellence and innovation. Our team of experts works tirelessly to provide solutions that exceed expectations and drive meaningful results for our clients.</p>
                    </div>
                    <div class="image-content">
                        <div class="image-container" data-field="about.image">
                            <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="About Us">
                            <button class="image-edit-btn hidden">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                                    <circle cx="12" cy="13" r="4"></circle>
                                </svg>
                            </button>
                            <input type="file" class="image-upload-input hidden" accept="image/*">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="services-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="editable-text" data-field="services.heading">Our Services</h2>
                    <p class="editable-text" data-field="services.description">Discover what we can do for you with our comprehensive range of professional services</p>
                </div>
                <div class="services-grid" id="servicesGrid">
                    <!-- Service items will be dynamically generated -->
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section id="testimonials" class="testimonials-section">
            <div class="container">
                <h2 class="editable-text" data-field="testimonials.heading">What Our Clients Say</h2>
                <div class="testimonials-grid" id="testimonialsGrid">
                    <!-- Testimonial items will be dynamically generated -->
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact-section">
            <div class="container">
                <div class="contact-content">
                    <h2 class="editable-text" data-field="contact.heading">Get In Touch</h2>
                    <p class="editable-text" data-field="contact.description">Ready to start your project? Contact us today and let's discuss how we can help you achieve your goals!</p>
                    <a href="mailto:<EMAIL>" class="btn btn--primary btn--lg editable-button" data-text-field="contact.buttonText" data-url-field="contact.buttonUrl">Contact Us</a>
                </div>
            </div>
        </section>
    </main>

    <script src="app.js"></script>
</body>
</html>